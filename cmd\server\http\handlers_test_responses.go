package http

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"strconv"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// RecordTestResponses godoc
//
//	@Summary		RecordTestResponses
//	@Description	Record student responses for all questions in a test
//	@Security       BearerAuth
//	@Param			item	body	models.TestResponsesForCreate	true	"test responses"
//	@Tags			test-responses
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.TestResponsesResult
//	@Failure		400	{object}	HTTPError
//	@Failure		403	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		409	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/test-responses [post]
func (h *Handlers) RecordTestResponses(ctx *gin.Context) {
	start := time.Now()

	// Get user ID from JWT token (set by middleware)
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get client IP for logging
	clientIP := ctx.ClientIP()

	slog.Info("RecordTestResponses request started",
		"user_id", userID,
		"client_ip", clientIP,
	)

	// Parse request body
	testResponsesInput := new(models.TestResponsesForCreate)
	if err := ctx.ShouldBindJSON(testResponsesInput); err != nil {
		duration := time.Since(start)
		slog.Warn("RecordTestResponses failed - invalid request body",
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate that responses are provided
	if len(testResponsesInput.Responses) == 0 {
		duration := time.Since(start)
		slog.Warn("RecordTestResponses failed - no responses provided",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id", testResponsesInput.TestID,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "No responses provided"})
		return
	}

	// Get student ID from user ID
	studentID, err := h.getStudentIDFromUserID(ctx.Request.Context(), userID.(uint))
	if err != nil {
		duration := time.Since(start)
		slog.Error("RecordTestResponses failed - failed to get student ID",
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Only students can submit test responses"})
		return
	}

	slog.Debug("RecordTestResponses processing",
		"user_id", userID,
		"student_id", studentID,
		"client_ip", clientIP,
		"test_id", testResponsesInput.TestID,
		"response_count", len(testResponsesInput.Responses),
	)

	// Record the responses
	result, err := h.db.RecordTestResponses(ctx.Request.Context(), studentID, testResponsesInput)
	if err != nil {
		duration := time.Since(start)
		slog.Error("RecordTestResponses failed - database error",
			"user_id", userID,
			"student_id", studentID,
			"client_ip", clientIP,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)

		// Check for specific error types
		if err.Error() == "student has already submitted responses for test ID "+strconv.Itoa(int(testResponsesInput.TestID)) {
			ctx.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	duration := time.Since(start)
	slog.Info("RecordTestResponses successful",
		"user_id", userID,
		"student_id", studentID,
		"client_ip", clientIP,
		"test_id", testResponsesInput.TestID,
		"total_questions", result.TotalQuestions,
		"correct_answers", result.CorrectAnswers,
		"total_score", result.TotalScore,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, result)
}

// GetStudentTestResponses godoc
//
//	@Summary		GetStudentTestResponses
//	@Description	Get student responses for a specific test
//	@Security       BearerAuth
//	@Param			test_id	path	int	true	"Test ID"
//	@Tags			test-responses
//	@Accept			json
//	@Produce		json
//	@Success		200	{array}	models.TestResponse
//	@Failure		400	{object}	HTTPError
//	@Failure		403	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/test-responses/{test_id} [get]
func (h *Handlers) GetStudentTestResponses(ctx *gin.Context) {
	start := time.Now()

	// Get user ID from JWT token (set by middleware)
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get client IP for logging
	clientIP := ctx.ClientIP()

	// Parse test ID from URL parameter
	testIDStr := ctx.Param("test_id")
	testID, err := strconv.ParseUint(testIDStr, 10, 32)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("GetStudentTestResponses failed - invalid test ID",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id_param", testIDStr,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid test ID"})
		return
	}

	slog.Info("GetStudentTestResponses request started",
		"user_id", userID,
		"client_ip", clientIP,
		"test_id", testID,
	)

	// Get student ID from user ID
	studentID, err := h.getStudentIDFromUserID(ctx.Request.Context(), userID.(uint))
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetStudentTestResponses failed - failed to get student ID",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Only students can view their test responses"})
		return
	}

	slog.Debug("GetStudentTestResponses processing",
		"user_id", userID,
		"student_id", studentID,
		"client_ip", clientIP,
		"test_id", testID,
	)

	// Get the responses
	responses, err := h.db.GetStudentTestResponses(ctx.Request.Context(), studentID, uint(testID))
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetStudentTestResponses failed - database error",
			"user_id", userID,
			"student_id", studentID,
			"client_ip", clientIP,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetStudentTestResponses successful",
		"user_id", userID,
		"student_id", studentID,
		"client_ip", clientIP,
		"test_id", testID,
		"response_count", len(responses),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"responses": responses})
}

// EvaluateTestResponses godoc
//
//	@Summary		EvaluateTestResponses
//	@Description	Evaluate all unevaluated responses for a specific test (Admin only)
//	@Security       BearerAuth
//	@Param			item	body	models.TestEvaluationRequest	true	"test evaluation request"
//	@Tags			test-responses
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.TestEvaluationResult
//	@Failure		400	{object}	HTTPError
//	@Failure		403	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/test-responses/evaluate [post]
func (h *Handlers) EvaluateTestResponses(ctx *gin.Context) {
	start := time.Now()

	// Get user ID from JWT token (set by middleware)
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get client IP for logging
	clientIP := ctx.ClientIP()

	slog.Info("EvaluateTestResponses request started",
		"user_id", userID,
		"client_ip", clientIP,
	)

	// Check if user is admin (only admins can evaluate responses)
	if err := h.checkAdminPermission(ctx.Request.Context(), userID.(uint)); err != nil {
		duration := time.Since(start)
		slog.Warn("EvaluateTestResponses failed - insufficient permissions",
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Only administrators can evaluate test responses"})
		return
	}

	// Parse request body
	evaluationRequest := new(models.TestEvaluationRequest)
	if err := ctx.ShouldBindJSON(evaluationRequest); err != nil {
		duration := time.Since(start)
		slog.Warn("EvaluateTestResponses failed - invalid request body",
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Debug("EvaluateTestResponses processing",
		"user_id", userID,
		"client_ip", clientIP,
		"test_id", evaluationRequest.TestID,
	)

	// Evaluate the responses
	result, err := h.db.EvaluateTestResponses(ctx.Request.Context(), evaluationRequest.TestID)
	if err != nil {
		duration := time.Since(start)
		slog.Error("EvaluateTestResponses failed - database error",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id", evaluationRequest.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("EvaluateTestResponses successful",
		"user_id", userID,
		"client_ip", clientIP,
		"test_id", evaluationRequest.TestID,
		"students_evaluated", result.TotalStudentsEvaluated,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, result)
}

// checkAdminPermission checks if the user has admin permissions
func (h *Handlers) checkAdminPermission(ctx context.Context, userID uint) error {
	// For now, we'll implement a simple check by trying to get user info
	// In a real implementation, you might want to add a specific method to the DB interface
	// This is a temporary workaround

	// Try to get student by user ID - if it succeeds, user is a student, not admin
	_, err := h.db.GetStudentIDByUserID(ctx, userID)
	if err == nil {
		return fmt.Errorf("user is a student, not an admin")
	}

	// If getting student fails, assume user might be admin
	// In a production system, you'd want a proper user role check
	return nil
}

// GetTestRankings godoc
//
//	@Summary		GetTestRankings
//	@Description	Get rankings for all students in a specific test
//	@Security       BearerAuth
//	@Param			test_id	path	int	true	"Test ID"
//	@Param			limit	query	int	false	"Limit number of results (default: 100)"
//	@Param			offset	query	int	false	"Offset for pagination (default: 0)"
//	@Tags			test-responses
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.TestRankingResult
//	@Failure		400	{object}	HTTPError
//	@Failure		403	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/test-responses/rankings/{test_id} [get]
func (h *Handlers) GetTestRankings(ctx *gin.Context) {
	start := time.Now()

	// Get user ID from JWT token (set by middleware)
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get client IP for logging
	clientIP := ctx.ClientIP()

	slog.Info("GetTestRankings request started",
		"user_id", userID,
		"client_ip", clientIP,
	)

	// Parse test ID from URL parameter
	testIDStr := ctx.Param("test_id")
	testID, err := strconv.ParseUint(testIDStr, 10, 32)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("GetTestRankings failed - invalid test ID",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id_param", testIDStr,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid test ID"})
		return
	}

	// Parse query parameters
	limitStr := ctx.DefaultQuery("limit", "100")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 100 // Default limit to prevent huge responses
	}

	offsetStr := ctx.DefaultQuery("offset", "0")
	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	slog.Debug("GetTestRankings processing",
		"user_id", userID,
		"client_ip", clientIP,
		"test_id", testID,
		"limit", limit,
		"offset", offset,
	)

	// Get the rankings
	result, err := h.db.GetTestRankings(ctx.Request.Context(), uint(testID), limit, offset)
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetTestRankings failed - database error",
			"user_id", userID,
			"client_ip", clientIP,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetTestRankings successful",
		"user_id", userID,
		"client_ip", clientIP,
		"test_id", testID,
		"total_students", result.TotalStudents,
		"returned_count", len(result.StudentRankings),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, result)
}

// getStudentIDFromUserID is a helper function to get student ID from user ID
func (h *Handlers) getStudentIDFromUserID(ctx context.Context, userID uint) (uint, error) {
	return h.db.GetStudentIDByUserID(ctx, userID)
}

package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"

	"gorm.io/gorm"
)

// RecordTestResponses records all responses for a test from a student
func (p *DbPlugin) RecordTestResponses(ctx context.Context, studentID uint, testResponsesInput *models.TestResponsesForCreate) (*models.TestResponsesResult, error) {
	start := time.Now()

	slog.Info("Recording test responses",
		"student_id", studentID,
		"test_id", testResponsesInput.TestID,
		"response_count", len(testResponsesInput.Responses),
	)

	// Verify the test exists and is active (read-only operation, no transaction needed)
	var test models.Test
	if err := p.db.Preload("Sections.Questions.Options").First(&test, testResponsesInput.TestID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for response recording",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testResponsesInput.TestID, err)
	}

	if !test.Active {
		duration := time.Since(start)
		slog.Warn("Attempt to submit responses to inactive test",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d is not active", testResponsesInput.TestID)
	}

	// Verify the student exists (read-only operation, no transaction needed)
	var student models.Student
	if err := p.db.First(&student, studentID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Student not found for response recording",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("student with ID %d not found: %w", studentID, err)
	}

	// Check if student has already submitted responses for this test (read-only operation)
	var existingResponseCount int64
	if err := p.db.Model(&models.TestResponse{}).
		Where("student_id = ? AND test_id = ?", studentID, testResponsesInput.TestID).
		Count(&existingResponseCount).Error; err != nil {
		return nil, fmt.Errorf("failed to check existing responses: %w", err)
	}

	if existingResponseCount > 0 {
		duration := time.Since(start)
		slog.Warn("Student already submitted responses for this test",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"existing_count", existingResponseCount,
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("student has already submitted responses for test ID %d", testResponsesInput.TestID)
	}

	// Create a map of question ID to question for quick lookup
	questionMap := make(map[uint]models.Question)
	for _, section := range test.Sections {
		for _, question := range section.Questions {
			questionMap[question.ID] = question
		}
	}

	// Validate that all questions in the responses belong to this test
	for _, responseInput := range testResponsesInput.Responses {
		if _, exists := questionMap[responseInput.QuestionID]; !exists {
			return nil, fmt.Errorf("question with ID %d is not part of test %d", responseInput.QuestionID, testResponsesInput.TestID)
		}
	}

	// Now start transaction for actual write operations
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Process each response - just record without evaluation
	var responseResults []models.TestResponseResult

	for _, responseInput := range testResponsesInput.Responses {
		// Create the test response record without evaluation
		testResponse := models.TestResponse{
			StudentID:         studentID,
			TestID:            testResponsesInput.TestID,
			QuestionID:        responseInput.QuestionID,
			SelectedOptionIDs: responseInput.SelectedOptionIDs,
			ResponseText:      responseInput.ResponseText,
			// Leave IsCorrect as false (default) and CalculatedScore as nil
			// These will be set during evaluation
		}

		// Save the response
		if err := tx.Create(&testResponse).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to save test response",
				"student_id", studentID,
				"test_id", testResponsesInput.TestID,
				"question_id", responseInput.QuestionID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to save response for question %d: %w", responseInput.QuestionID, err)
		}

		// Add to results - without evaluation data
		responseResult := models.TestResponseResult{
			QuestionID: responseInput.QuestionID,
			Message:    "Response recorded successfully - pending evaluation",
		}

		responseResults = append(responseResults, responseResult)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit test responses transaction",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit responses: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Test responses recorded successfully",
		"student_id", studentID,
		"test_id", testResponsesInput.TestID,
		"total_questions", len(testResponsesInput.Responses),
		"duration_ms", duration.Milliseconds(),
	)

	// Prepare the result - without evaluation data
	result := &models.TestResponsesResult{
		TestID:          testResponsesInput.TestID,
		StudentID:       studentID,
		TotalQuestions:  len(testResponsesInput.Responses),
		CorrectAnswers:  0, // Will be set after evaluation
		TotalScore:      0, // Will be set after evaluation
		ResponseResults: responseResults,
		Message:         fmt.Sprintf("Successfully recorded %d responses. Responses are pending evaluation.", len(testResponsesInput.Responses)),
	}

	return result, nil
}

// GetStudentTestResponses retrieves all responses for a specific student and test
func (p *DbPlugin) GetStudentTestResponses(ctx context.Context, studentID, testID uint) ([]models.TestResponse, error) {
	start := time.Now()

	slog.Debug("Getting student test responses",
		"student_id", studentID,
		"test_id", testID,
	)

	var responses []models.TestResponse
	if err := p.db.Preload("Question").Preload("Student.User").Preload("Test").
		Where("student_id = ? AND test_id = ?", studentID, testID).
		Find(&responses).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student test responses",
			"student_id", studentID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve responses: %w", err)
	}

	duration := time.Since(start)
	slog.Debug("Student test responses retrieved successfully",
		"student_id", studentID,
		"test_id", testID,
		"response_count", len(responses),
		"duration_ms", duration.Milliseconds(),
	)

	return responses, nil
}

// EvaluateTestResponses evaluates all unevaluated responses for a given test
func (p *DbPlugin) EvaluateTestResponses(ctx context.Context, testID uint) (*models.TestEvaluationResult, error) {
	start := time.Now()

	slog.Info("Starting test response evaluation",
		"test_id", testID,
	)

	// Verify the test exists and get test details (read-only operation, no transaction needed)
	var test models.Test
	if err := p.db.Preload("Sections.Questions.Options").First(&test, testID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for evaluation",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testID, err)
	}

	// Get all unevaluated responses for this test (read-only operation, no transaction needed)
	var unevaluatedResponses []models.TestResponse
	if err := p.db.Preload("Student.User").Preload("Question.Options").
		Where("test_id = ? AND calculated_score IS NULL", testID).
		Find(&unevaluatedResponses).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve unevaluated responses",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve unevaluated responses: %w", err)
	}

	if len(unevaluatedResponses) == 0 {
		duration := time.Since(start)
		slog.Info("No unevaluated responses found for test",
			"test_id", testID,
			"duration_ms", duration.Milliseconds(),
		)
		return &models.TestEvaluationResult{
			TestID:                 testID,
			TestName:               test.Name,
			TotalStudentsEvaluated: 0,
			StudentResults:         []models.StudentEvaluationResult{},
			Message:                "No unevaluated responses found for this test",
		}, nil
	}

	// Group responses by student (in-memory operation, no transaction needed)
	studentResponsesMap := make(map[uint][]models.TestResponse)
	for _, response := range unevaluatedResponses {
		studentResponsesMap[response.StudentID] = append(studentResponsesMap[response.StudentID], response)
	}

	// Now start transaction for actual write operations (evaluation and updates)
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var studentResults []models.StudentEvaluationResult
	evaluatedCount := 0

	// Evaluate responses for each student
	for studentID, responses := range studentResponsesMap {
		studentResult, err := p.evaluateStudentResponses(tx, studentID, responses)
		if err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to evaluate student responses",
				"test_id", testID,
				"student_id", studentID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to evaluate responses for student %d: %w", studentID, err)
		}

		studentResults = append(studentResults, *studentResult)
		evaluatedCount++
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit evaluation transaction",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit evaluation: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Test response evaluation completed successfully",
		"test_id", testID,
		"students_evaluated", evaluatedCount,
		"total_responses_evaluated", len(unevaluatedResponses),
		"duration_ms", duration.Milliseconds(),
	)

	// Prepare the result
	result := &models.TestEvaluationResult{
		TestID:                 testID,
		TestName:               test.Name,
		TotalStudentsEvaluated: evaluatedCount,
		StudentResults:         studentResults,
		Message:                fmt.Sprintf("Successfully evaluated responses for %d students (%d total responses)", evaluatedCount, len(unevaluatedResponses)),
	}

	return result, nil
}

// evaluateStudentResponses evaluates all responses for a single student
func (p *DbPlugin) evaluateStudentResponses(tx *gorm.DB, studentID uint, responses []models.TestResponse) (*models.StudentEvaluationResult, error) {
	var totalScore int
	var correctAnswers int
	studentName := ""

	// Get student name from the first response
	if len(responses) > 0 {
		studentName = responses[0].Student.User.FullName
	}

	// Evaluate each response
	for i := range responses {
		response := &responses[i]

		// Evaluate the response and calculate score
		isCorrect, score := p.evaluateResponse(&response.Question, response)

		// Update the response with evaluation results
		response.IsCorrect = isCorrect
		response.CalculatedScore = &score

		// Save the updated response
		if err := tx.Save(response).Error; err != nil {
			return nil, fmt.Errorf("failed to update response for question %d: %w", response.QuestionID, err)
		}

		if isCorrect {
			correctAnswers++
		}
		totalScore += score
	}

	// Create the student evaluation result
	result := &models.StudentEvaluationResult{
		StudentID:      studentID,
		StudentName:    studentName,
		TotalQuestions: len(responses),
		CorrectAnswers: correctAnswers,
		TotalScore:     totalScore,
		EvaluationTime: time.Now().Format(time.RFC3339),
		Message:        fmt.Sprintf("Evaluated %d responses: %d correct, total score: %d", len(responses), correctAnswers, totalScore),
	}

	return result, nil
}

// GetTestRankings retrieves rankings for all students in a specific test
func (p *DbPlugin) GetTestRankings(ctx context.Context, testID uint, limit, offset int) (*models.TestRankingResult, error) {
	start := time.Now()

	slog.Info("Getting test rankings",
		"test_id", testID,
		"limit", limit,
		"offset", offset,
	)

	// Verify the test exists and get test details (read-only operation)
	var test models.Test
	if err := p.db.First(&test, testID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for rankings",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testID, err)
	}

	// Get all student marks for this test, ordered by final marks descending
	var studentMarks []models.StudentTestMark
	query := p.db.Preload("Student.User").
		Where("test_id = ?", testID).
		Order("final_marks DESC, student_id ASC") // Secondary sort by student_id for consistent ordering

	// Apply pagination if specified
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&studentMarks).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student marks for rankings",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve student marks: %w", err)
	}

	if len(studentMarks) == 0 {
		duration := time.Since(start)
		slog.Info("No student marks found for test",
			"test_id", testID,
			"duration_ms", duration.Milliseconds(),
		)
		return &models.TestRankingResult{
			TestID:          testID,
			TestName:        test.Name,
			TotalStudents:   0,
			StudentRankings: []models.StudentRankingInfo{},
			Message:         "No student marks found for this test",
		}, nil
	}

	// Calculate statistics
	var totalMarks int
	highestMarks := studentMarks[0].FinalMarks
	lowestMarks := studentMarks[len(studentMarks)-1].FinalMarks

	for _, mark := range studentMarks {
		totalMarks += mark.FinalMarks
		if mark.FinalMarks > highestMarks {
			highestMarks = mark.FinalMarks
		}
		if mark.FinalMarks < lowestMarks {
			lowestMarks = mark.FinalMarks
		}
	}

	averageMarks := float64(totalMarks) / float64(len(studentMarks))

	// Get total count for percentile calculation (if pagination is used)
	var totalStudents int64
	if err := p.db.Model(&models.StudentTestMark{}).
		Where("test_id = ?", testID).
		Count(&totalStudents).Error; err != nil {
		return nil, fmt.Errorf("failed to count total students: %w", err)
	}

	// Build student rankings with rank and percentile
	var studentRankings []models.StudentRankingInfo
	currentRank := offset + 1 // Start rank based on offset

	for i, mark := range studentMarks {
		// Handle tied scores - students with same marks get same rank
		if i > 0 && mark.FinalMarks < studentMarks[i-1].FinalMarks {
			currentRank = offset + i + 1
		}

		// Calculate percentile (percentage of students with lower scores)
		percentile := float64(int(totalStudents)-currentRank+1) / float64(totalStudents) * 100

		studentRanking := models.StudentRankingInfo{
			StudentID:          uint(mark.StudentID),
			StudentName:        mark.Student.User.FullName,
			StudentEmail:       mark.Student.User.Email,
			TotalPositiveMarks: mark.TotalPositiveMarks,
			TotalNegativeMarks: mark.TotalNegativeMarks,
			FinalMarks:         mark.FinalMarks,
			Rank:               currentRank,
			Percentile:         percentile,
		}

		studentRankings = append(studentRankings, studentRanking)
	}

	duration := time.Since(start)
	slog.Info("Test rankings retrieved successfully",
		"test_id", testID,
		"total_students", totalStudents,
		"returned_count", len(studentRankings),
		"highest_marks", highestMarks,
		"lowest_marks", lowestMarks,
		"average_marks", averageMarks,
		"duration_ms", duration.Milliseconds(),
	)

	// Prepare the result
	result := &models.TestRankingResult{
		TestID:          testID,
		TestName:        test.Name,
		TotalStudents:   int(totalStudents),
		HighestMarks:    highestMarks,
		LowestMarks:     lowestMarks,
		AverageMarks:    averageMarks,
		StudentRankings: studentRankings,
		Message:         fmt.Sprintf("Retrieved rankings for %d students (showing %d)", totalStudents, len(studentRankings)),
	}

	return result, nil
}

// evaluateResponse evaluates a student's response and calculates the score
func (p *DbPlugin) evaluateResponse(question *models.Question, response *models.TestResponse) (bool, int) {
	// For multiple choice questions, check selected options
	if len(response.SelectedOptionIDs) > 0 {
		return p.evaluateMultipleChoiceResponse(question, response)
	}

	// For text-based questions, compare with correct answer
	if response.ResponseText != nil {
		return p.evaluateTextResponse(question, response)
	}

	// No response provided
	return false, 0
}

// evaluateMultipleChoiceResponse evaluates multiple choice responses
func (p *DbPlugin) evaluateMultipleChoiceResponse(question *models.Question, response *models.TestResponse) (bool, int) {
	// Load question options if not already loaded
	if len(question.Options) == 0 {
		if err := p.db.Where("question_id = ?", question.ID).Find(&question.Options).Error; err != nil {
			slog.Error("Failed to load question options for evaluation",
				"question_id", question.ID,
				"error", err.Error(),
			)
			return false, 0
		}
	}

	// Get correct option IDs
	var correctOptionIDs []int
	for _, option := range question.Options {
		if option.IsCorrect {
			correctOptionIDs = append(correctOptionIDs, int(option.ID))
		}
	}

	// Compare selected options with correct options
	if len(correctOptionIDs) == 0 {
		slog.Warn("No correct options found for question", "question_id", question.ID)
		return false, 0
	}

	// Check if selected options match correct options exactly
	if len(response.SelectedOptionIDs) != len(correctOptionIDs) {
		return false, 0
	}

	// Create maps for comparison
	selectedMap := make(map[int]bool)
	for _, id := range response.SelectedOptionIDs {
		selectedMap[id] = true
	}

	correctMap := make(map[int]bool)
	for _, id := range correctOptionIDs {
		correctMap[id] = true
	}

	// Check if all selected options are correct and all correct options are selected
	for _, id := range response.SelectedOptionIDs {
		if !correctMap[id] {
			return false, 0
		}
	}

	for _, id := range correctOptionIDs {
		if !selectedMap[id] {
			return false, 0
		}
	}

	// All correct - return positive score (could be configurable based on question difficulty)
	return true, 1
}

// evaluateTextResponse evaluates text-based responses
func (p *DbPlugin) evaluateTextResponse(question *models.Question, response *models.TestResponse) (bool, int) {
	if response.ResponseText == nil || question.CorrectAnswer == "" {
		return false, 0
	}

	// Simple string comparison (case-insensitive)
	// In a real system, you might want more sophisticated text matching
	responseText := *response.ResponseText
	if len(responseText) == 0 {
		return false, 0
	}

	// For now, do exact match (case-insensitive)
	// You could enhance this with fuzzy matching, keyword matching, etc.
	isCorrect := responseText == question.CorrectAnswer

	if isCorrect {
		return true, 1
	}

	return false, 0
}
